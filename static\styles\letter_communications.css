/* Styles specific to the letter communications view */

/* Map container styling */
.map-container-communications {
    position: relative;
    width: 90vw; /* 90% of viewport width */
    height: calc(100vh - 150px); /* Adjust height to account for selector and description */
    overflow: hidden;
    margin: 0 auto; /* Center the map container */
    max-width: 1200px; /* Set a max-width for larger screens */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Add subtle shadow for depth */
    border-radius: 8px; /* Add rounded corners */
    left: 50%;
    transform: translateX(-50%); /* Center the container regardless of parent */
}

#communications-map {
    width: 100%;
    height: 100%;
}

/* Section header to contain title and selector stacked vertically */
.section-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: 15px;
}

.section-header .section-subtitle-wrapper {
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
}

#diplomat-selector-container {
    width: 90%; /* Set width to 90% */
    overflow: visible; /* Changed from hidden to visible */
    margin: 5px auto; /* Center the selector container */
    padding: 10px 0;
    font-family: 'Inter', sans-serif;
    z-index: 1000;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    max-width: 1200px; /* Set a max-width for larger screens */
}

#diplomat-selector-heading {
    font-size: 1.6rem;
    margin-top: 20px;
    margin-bottom: 20px;
    color: var(--primary);
    position: relative;
    padding: 0;
    font-family: 'Playfair Display', serif;
    text-align: center; /* Center the text */
    display: inline-block; /* Keep as inline-block */
}

#diplomat-selector-heading::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%); /* Center the underline */
    width: 80px;
    height: 2px;
    background-color: var(--accent);
    border-radius: 2px;
}

/* Custom styling for the dropdown */
#diplomat-selector {
    width: 100%;
    padding: 12px 15px;
    border-radius: 6px;
    border: 1px solid var(--accent);
    font-size: 15px;
    font-family: 'Inter', sans-serif;
    color: var(--neutral-dark);
    background-color: var(--neutral-light);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-top: 5px;
    max-width: none; /* Ensure no max-width restriction */
    font-weight: 500; /* Slightly bolder text */
    cursor: pointer;
    
    padding-right: 40px; /* Make room for the custom arrow */
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* Remove default arrow in IE */
#diplomat-selector::-ms-expand {
    display: none;
}

/* Style the dropdown options */
#diplomat-selector option {
    padding: 8px;
    background-color: var(--neutral-light);
    color: var(--neutral-dark);
    font-family: 'Inter', sans-serif;
}

/* Limit dropdown height in Chrome/Safari */
select#diplomat-selector {
    background-color: var(--neutral-light);
}

/* This is a hack to limit dropdown height in modern browsers */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    select#diplomat-selector {
        height: 40px; /* Height of the select element */
    }

    select#diplomat-selector[size]:focus {
        height: 200px; /* Height of the dropdown when open */
    }
}

/* For Firefox */
@-moz-document url-prefix() {
    #diplomat-selector {
        overflow-y: auto;
        max-height: 200px; /* Reduced from 300px */
        scrollbar-width: thin;
        scrollbar-color: var(--accent) #f1f1f1;
    }
}

/* For Chrome and other browsers that support these properties */
#diplomat-selector::-webkit-scrollbar {
    width: 6px; /* Thinner scrollbar */
}

#diplomat-selector::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#diplomat-selector::-webkit-scrollbar-thumb {
    background: var(--accent);
    border-radius: 4px;
}

#diplomat-selector::-webkit-scrollbar-thumb:hover {
    background: var(--secondary);
}

#diplomat-selector:focus {
    border-color: var(--secondary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(166, 123, 91, 0.25);
}

/* Diplomat description box below the selector */
#diplomat-description {
    width: 90%;
    overflow: hidden;
    margin: 0 auto; /* Center the map container */
    max-width: 1200px; /* Set a max-width for larger screens */
    padding: 15px 20px;
    background-color: var(--mint-beige);
    border-radius: 6px;
    font-family: 'Inter', sans-serif;
    line-height: 1.5;
    color: var(--neutral-dark);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    margin-top: 15px;
    margin-bottom: 20px;
}

#diplomat-description .initial-message {
    color: #888;
    font-style: italic;
    text-align: center;
    padding: 10px 0;
    font-size: 16px;
}
#diplomat-selector:-ms-expand {
    display: none;
}
/* Communications info panel */
#communications-info {
    position: absolute;
    top: 20px; /* Position at top left */
    left: 20px;
    z-index: 1000;
    background-color: var(--neutral-light);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.25);
    max-width: 300px; /* Smaller width */
    max-height: 300px; /* Smaller height */
    overflow-y: auto;
    border: 1px solid var(--accent);
    font-family: 'Inter', sans-serif;
    /* Envelope styling */
    background-image: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(245,245,245,0.8) 100%);
    border-top-right-radius: 0; /* Envelope flap effect */
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

#communications-info::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 0;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 0 solid transparent;
    border-bottom: 10px solid var(--accent);
    transform: rotate(0deg);
    z-index: -1;
}

#communications-info h3 {
    margin-top: 0;
    margin-bottom: 12px;
    font-family: 'Playfair Display', serif;
    color: var(--primary);
    font-size: 18px;
    letter-spacing: 0.5px;
    position: relative;
    padding-bottom: 8px;
}

#communications-info h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(to right, var(--accent), var(--primary));
    border-radius: 2px;
}

/* Style the event items to look like letter entries */
.event-item {
    padding: 8px;
    margin-bottom: 8px;
    border-bottom: 1px dashed var(--accent);
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 4px;
}

.event-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.event-date {
    font-weight: bold;
    font-size: 14px;
    color: var(--primary);
    margin-bottom: 4px;
}

.event-content {
    font-size: 13px;
    line-height: 1.4;
}

.event-content p {
    margin: 4px 0;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    #communications-info {
        max-width: 250px;
        max-height: 250px;
    }
}

@media screen and (max-width: 480px) {
    #communications-info {
        max-width: 200px;
        max-height: 200px;
        padding: 10px;
        top: 10px;
        left: 10px;
    }
    
    .event-item {
        padding: 6px;
        margin-bottom: 6px;
    }
    
    .event-date {
        font-size: 12px;
    }
    
    .event-content {
        font-size: 11px;
    }
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .section-container {
        padding: 0 20px;
    }

    .section-header {
        flex-direction: column;
        align-items: center;
    }

    #diplomat-selector-container {
        width: 100%;
        margin-top: 10px;
        padding: 5px 0;
    }

    #diplomat-selector-heading {
        font-size: 1.4rem;
        margin-top: 15px;
        margin-bottom: 15px;
    }

    #diplomat-selector {
        width: 100%;
    }

    #diplomat-description {
        padding: 12px 15px;
        margin-top: 10px;
        margin-bottom: 15px;
    }

    #communications-info {
        max-width: 350px;
        max-height: 250px;
        top: 60px;
        left: 15px;
    }

    .map-container-communications {
        height: calc(100vh - 220px); /* Adjust for taller selector container */
    }
}

@media screen and (max-width: 480px) {
    .section-container {
        padding: 0 15px;
    }

    #diplomat-selector-heading {
        font-size: 1.3rem;
        margin-top: 12px;
        margin-bottom: 12px;
    }

    .section-header {
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }

    #diplomat-selector-container {
        padding: 5px 0;
        width: 100%;
    }

    #diplomat-selector {
        font-size: 14px;
        padding: 6px;
        width: 100%;
    }

    #diplomat-description {
        padding: 10px 12px;
        font-size: 14px;
        width: 100%;
        margin-top: 8px;
        margin-bottom: 12px;
    }

    #communications-info {
        max-width: 200px;
        max-height: 150px;
        padding: 10px;
    }

    .map-container-communications {
        height: calc(100vh - 180px);
    }
}

/* Diplomat Gallery Slider Styles */
.diplomat-gallery-section {
    margin: 30px 0;
    padding: 20px 0;
}

.diplomat-gallery-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    overflow: hidden;
}

.diplomat-gallery {
    display: flex;
    transition: transform 0.5s ease-in-out;
    gap: 20px;
    padding: 20px 0;
}

.diplomat-slide {
    min-width: 250px;
    max-width: 250px;
    background: var(--neutral-light);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.diplomat-slide:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.diplomat-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-bottom: 2px solid var(--accent);
}

.diplomat-info {
    padding: 15px;
    text-align: center;
}

.diplomat-name {
    font-family: 'Playfair Display', serif;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 5px;
    line-height: 1.3;
}

.diplomat-title {
    font-family: 'Inter', sans-serif;
    font-size: 12px;
    color: var(--neutral-dark);
    font-style: italic;
    line-height: 1.4;
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.gallery-nav:hover {
    background: var(--accent);
    color: white;
    transform: translateY(-50%) scale(1.1);
}

.gallery-nav.prev {
    left: 10px;
}

.gallery-nav.next {
    right: 10px;
}

.gallery-nav i {
    font-size: 18px;
    color: var(--primary);
}

.gallery-nav:hover i {
    color: white;
}

.gallery-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.gallery-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--accent);
    opacity: 0.3;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.gallery-dot.active {
    opacity: 1;
}

/* Responsive gallery styles */
@media screen and (max-width: 768px) {
    .diplomat-slide {
        min-width: 200px;
        max-width: 200px;
    }
    
    .diplomat-image {
        height: 250px;
    }
    
    .diplomat-name {
        font-size: 14px;
    }
    
    .diplomat-title {
        font-size: 11px;
    }
    
    .gallery-nav {
        width: 40px;
        height: 40px;
    }
    
    .gallery-nav i {
        font-size: 16px;
    }
}

@media screen and (max-width: 480px) {
    .diplomat-slide {
        min-width: 180px;
        max-width: 180px;
    }
    
    .diplomat-image {
        height: 220px;
    }
    
    .diplomat-info {
        padding: 12px;
    }
    
    .diplomat-name {
        font-size: 13px;
    }
    
    .diplomat-title {
        font-size: 10px;
    }
    
    .gallery-nav {
        width: 35px;
        height: 35px;
    }
    
    .gallery-nav i {
        font-size: 14px;
    }
}

/* Enhanced popup styles for communication details */
.marker-popup .communications-list {
    margin-top: 10px;
    max-height: 200px;
    overflow-y: auto;
    border-top: 1px solid #e0e0e0;
    padding-top: 8px;
}

.marker-popup .communication-item {
    margin-bottom: 8px;
    padding: 6px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    border-left: 3px solid var(--accent);
}

.marker-popup .communication-item:last-child {
    margin-bottom: 0;
}

.marker-popup .communication-item small {
    display: block;
    line-height: 1.3;
    color: #666;
}

.marker-popup .communication-item small:first-child {
    font-weight: 600;
    color: var(--primary);
}

/* Scrollbar styling for communications list */
.communications-list::-webkit-scrollbar {
    width: 4px;
}

.communications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.communications-list::-webkit-scrollbar-thumb {
    background: var(--accent);
    border-radius: 2px;
}

.communications-list::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Custom marker styles with communication count */
.custom-marker .marker-icon {
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
}

.custom-marker .marker-count {
    color: white;
    font-weight: bold;
    font-size: 12px;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    line-height: 1;
}

/* Ensure marker icons are properly sized and non-interactive */
.custom-marker {
    background: transparent !important;
    border: none !important;
}

.custom-marker.non-interactive {
    pointer-events: none !important;
    cursor: default !important;
}

.custom-marker.non-interactive .marker-icon {
    cursor: default !important;
}
