/* Mobility Map specific styles */

/* Adjust the position of the criminal selector */
#criminal-selector-container {
    margin-top: -20px; /* Negative margin to move it up */
    position: relative;
    z-index: 1000; /* Ensure it stays above other elements */
}

/* Make sure the dropdown is visible */
#criminal-selector {
    width: 100%;
    padding: 12px 15px;
    border-radius: 6px;
    border: 1px solid var(--accent);
    font-size: 15px;
    font-family: 'Inter', sans-serif;
    color: var(--neutral-dark);
    background-color: var(--neutral-light);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-top: 5px;
    font-weight: 500;
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%231A2A40' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px;
    padding-right: 40px; /* Make room for the custom arrow */
}

/* Adjust the section header spacing */
.section-header {
    margin-bottom: 5px; /* Reduced from 15px */
}

/* Adjust the title spacing */
#criminal-selector-heading {
    margin-bottom: 10px; /* Reduced from 20px */
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    #criminal-selector-container {
        margin-top: -15px;
    }
    
    #criminal-selector-heading {
        margin-bottom: 8px;
    }
}

@media screen and (max-width: 480px) {
    #criminal-selector-container {
        margin-top: -10px;
    }
    
    #criminal-selector-heading {
        margin-bottom: 5px;
    }
}
