<!-- Interactive Beyoglu Map Content -->
<div class="content-container">
  <div class="main-content">
    <div class="map-section">

      
      <div class="map-content">
        <div class="map-container">
          <div id="modern-map" class="modern-map"></div>
          <img src="/static/images/beyoglu_map.png" alt="Interactive Historical map of Beyoğlu district (Galata, Pera, and Pancaldi) from 1882" class="historical-map">
          <div class="opacity-control">
            <label for="opacity-slider">Historical Map Opacity:</label>
            <input type="range" id="opacity-slider" min="0" max="100" value="80" class="opacity-slider">
            <span id="opacity-value">80%</span>
          </div>

          <div class="zoom-controls">
            <h4>Zoom Controls</h4>
            <div class="zoom-buttons">
              <button id="zoom-in" class="zoom-btn" title="Zoom In">+</button>
              <button id="zoom-out" class="zoom-btn" title="Zoom Out">−</button>
            </div>
            <div class="zoom-reset">
              <button id="reset-zoom" class="reset-btn" title="Reset Zoom">Reset</button>
            </div>
          </div>

        </div>
        
        <div class="legend-sidebar">

        
        <div class="legend-content">
          <div class="legend-section">
            <h4 class="section-title">Where Did They Live/Work? (The green circles on the map)</h4>
            
            <div class="section-content">
              <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>1: Giuseppe Civicoff</h4>
                <p>Hotel de France next to the French College, close to Petits de Champs</p>
                <span class="location-address">(Rue (street-cadde) 66: Çukurcuma)</span>
              </div>
            </div>
            
            <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>2: Giuseppe Lopetz</h4>
                <p>lives with his family, near the Court of Galata</p>
                <span class="location-address">(Rue 198: Mahkeme- Galata)</span>
              </div>
            </div>
            
            <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>3: Roberto Diamanti</h4>
                <p>works at a shop</p>
                <span class="location-address">(Rue 23: Yeniçarşı – Pera)</span>
              </div>
            </div>
            
            <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>4: Margarita – Francisco Petris</h4>
                <p>they live here</p>
                <span class="location-address">(Rue 200: Kalyoncu Kulluk)</span>
              </div>
            </div>
            
            <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>5: Gaetano Manzo</h4>
                <p>works at Café Prado across the Selimiye Barracks</p>
                <span class="location-address">(Rue 4: Taksim)</span>
              </div>
            </div>
            
            <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>6: Cesare Venzi – Andonaki Draganikos</h4>
                <p>they live close to Tekke</p>
                <span class="location-address">(Rue 1: Grand Rue de Pera)</span>
              </div>
            </div>
            
            <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>7: Cesare Venzi – Andonaki Draganikos</h4>
                <p>they work at a shop</p>
                <span class="location-address">(Rue 206: Tepebaşı)</span>
              </div>
            </div>
            
            <div class="location-item green">
              <div class="location-marker green"></div>
              <div class="location-info">
                <h4>8: Giovanni Romano</h4>
                <p>works at Button Maker Factory owned by Catherin Perin</p>
                <span class="location-address">(Rue 140: Yenişehir - Pera)</span>
              </div>
                          </div>
            </div>
          </div>
            
            <div class="legend-section">
              <h4 class="section-title">Where Did They Meet? (The purple circles on the map)</h4>
              
              <div class="section-content">
                <div class="location-item purple">
              <div class="location-marker purple"></div>
              <div class="location-info">
                <h4>1: Gaetano Manzo – Clitzi Cole</h4>
                <p>Café Charalanpas</p>
                <span class="location-address">(Rue 200: Kalyoncu Kulluk)</span>
              </div>
            </div>
            
            <div class="location-item purple">
              <div class="location-marker purple"></div>
              <div class="location-info">
                <h4>2: Ludovico Boschi – Gaetano Manzo – Tommasso Facchini</h4>
                <p>Bülbül Café</p>
                <span class="location-address">(Rue 157: Bülbül)</span>
              </div>
            </div>
            
            <div class="location-item purple">
              <div class="location-marker purple"></div>
              <div class="location-info">
                <h4>3: Ludovico Boschi – Gaetano Manzo – Cesare Venzi – Andonaki Draganikos</h4>
                <p>Café Tekke</p>
                <span class="location-address">(Rue 1: Grand Rue de Pera)</span>
              </div>
            </div>
            
            <div class="location-item purple">
              <div class="location-marker purple"></div>
              <div class="location-info">
                <h4>4: Ludovico Boschi – Gaetano Manzo – Cesare Venzi – Andonaki Draganikos</h4>
                <p>Liquor Factory nearby Taksim Fountain</p>
                <span class="location-address">(Rue 4: Taksim)</span>
              </div>
            </div>
            
            <div class="location-item purple">
              <div class="location-marker purple"></div>
              <div class="location-info">
                <h4>5: Clitzi Cole – Gaetano Manzo – Francisco Petris</h4>
                <p>English Casino across the Sardinian hospital</p>
              </div>
            </div>
            
            <div class="location-item purple">
              <div class="location-marker purple"></div>
              <div class="location-info">
                <h4>6: Ludovico Bosschi – Andonaki Draganikos</h4>
                <p>Theatre Café close to Naum Theatre and opposite to Hungarian Casino</p>
                <span class="location-address">(Rue 18: Theatre)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</div>

<style>
.content-container {
  max-width: calc(100vw - 20px);
  margin: 0 auto;
  padding: 30px;
  width: 100%;
  height: 900px;
  box-sizing: border-box;
  overflow: visible;
}

.main-content {
  width: 100%;
  min-width: 0;
  height: 850px;
}

.map-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--accent);
  max-width: calc(100vw - 60px);
  height: 850px;
  box-sizing: border-box;
}



.map-content {
  display: flex;
  gap: 20px;
  height: 700px;
}



.map-container {
  text-align: center;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 8px;
  flex: 3;
  min-width: 0;
  position: relative;
  /* Make container 3x longer */
  aspect-ratio: 3/1;
  max-width: none;
  max-height: none;
  padding: 60px 20px 20px 20px; /* Add top padding to prevent title overlap */
}

.modern-map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: 8px;
  transition: transform 0.3s ease;
  overflow: visible;
  /* Ensure rotated content is fully visible */
  transform-origin: center center;
}

.modern-map.rotated {
  transform-origin: center center;
}

.historical-map {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  object-fit: contain;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  border-radius: 5px;
  max-width: 100%;
  max-height: 100%;
  transition: opacity 0.3s ease, transform 0.3s ease;
  overflow: visible;
  transform-origin: center center;
}

.opacity-control {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 10px;
  font-family: 'Montserrat', sans-serif;
  font-size: 0.9rem;
}

.zoom-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  font-family: 'Montserrat', sans-serif;
}

.zoom-controls h4 {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  color: var(--primary);
  text-align: center;
}

.zoom-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;
}

.zoom-btn {
  width: 40px;
  height: 40px;
  border: 2px solid var(--accent);
  background: white;
  color: var(--accent);
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.zoom-btn:hover {
  background: var(--accent);
  color: white;
  transform: scale(1.1);
}

.zoom-btn:active {
  transform: scale(0.95);
}

.zoom-reset {
  text-align: center;
}

.reset-btn {
  padding: 8px 16px;
  border: 2px solid var(--accent);
  background: white;
  color: var(--accent);
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  font-family: 'Montserrat', sans-serif;
  transition: all 0.2s ease;
}

.reset-btn:hover {
  background: var(--accent);
  color: white;
  transform: scale(1.05);
}

.reset-btn:active {
  transform: scale(0.95);
}

.opacity-control label {
  color: var(--primary);
  font-weight: 500;
  white-space: nowrap;
}

.opacity-slider {
  width: 100px;
  height: 6px;
  border-radius: 3px;
  background: #e0e0e0;
  outline: none;
  cursor: pointer;
  border: 1px solid var(--accent);
}

.opacity-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent);
  border: 2px solid white;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.opacity-slider::-webkit-slider-thumb:hover {
  background: var(--primary);
  transform: scale(1.1);
}

.opacity-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent);
  border: 2px solid white;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.opacity-slider::-moz-range-thumb:hover {
  background: var(--primary);
  transform: scale(1.1);
}

#opacity-value {
  color: var(--neutral-dark);
  font-weight: 500;
  min-width: 30px;
}



.legend-sidebar {
  width: 280px;
  min-width: 250px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--accent);
  display: flex;
  flex-direction: column;
  height: 750px;
  flex-shrink: 0;
}

.legend-header {
  padding: 20px;
  border-bottom: 1px solid var(--accent);
  background: var(--primary);
  color: white;
  border-radius: 8px 8px 0 0;
}

.legend-header h3 {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  margin: 0;
  text-align: center;
}

.legend-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 100%;
}

.location-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  background: #f8f9fa;
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.location-item:hover {
  background: #e9ecef;
  transform: translateX(2px);
}

.location-item.red {
  border-left-color: #00EFA8;
}

.location-item.green {
  border-left-color: #00EFA8;
}

.location-item.purple {
  border-left-color: #BA7CFF;
}

.location-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 12px;
  margin-top: 4px;
  flex-shrink: 0;
  border: 2px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.location-marker.green {
  background-color: #00EFA8;
}

.location-marker.purple {
  background-color: #BA7CFF;
}

.location-info {
  flex: 1;
  min-width: 0;
}

.location-info h4 {
  font-family: 'Playfair Display', serif;
  color: var(--primary);
  font-size: 0.9rem;
  margin: 0 0 2px 0;
  font-weight: 600;
}

.location-info p {
  font-family: 'Montserrat', sans-serif;
  color: var(--neutral-dark);
  font-size: 0.8rem;
  margin: 0 0 2px 0;
  line-height: 1.2;
}

.legend-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-family: 'Playfair Display', serif;
  color: var(--primary);
  font-size: 1rem;
  margin: 0 0 10px 0;
  font-weight: 600;
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid var(--accent);
  flex-shrink: 0;
}

.section-content {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  position: relative;
}

.section-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, rgba(255, 255, 255, 0.8));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.section-content.has-overflow::after {
  opacity: 1;
}

.section-content::-webkit-scrollbar {
  width: 6px;
}

.section-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.section-content::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 3px;
}

.section-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

.location-address {
  font-family: 'Montserrat', sans-serif;
  color: var(--accent);
  font-size: 0.7rem;
  font-weight: 500;
  font-style: italic;
  display: block;
  margin-bottom: 0;
}

.location-note {
  font-family: 'Montserrat', sans-serif;
  color: #666;
  font-size: 0.75rem;
  font-style: italic;
  display: block;
}

@media (max-width: 1024px) {
  .map-content {
    flex-direction: column;
    gap: 15px;
    height: auto;
    min-height: 70vh;
  }
  
  .legend-sidebar {
    width: 100%;
    height: 300px;
  }
  
  .map-container {
    height: 60vh;
    min-height: 500px;
    /* Remove fixed aspect ratio for rotated content */
    max-width: none;
    max-height: none;
    margin: 0;
  }
  
  .historical-map {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .content-container {
    padding: 15px;
  }
  
  .map-section {
    padding: 20px;
  }
  
  .map-section h2 {
    font-size: 1.3rem;
  }
  
  .map-content {
    height: 60vh;
    min-height: 400px;
  }
  
  .map-container {
    height: 50vh;
    min-height: 400px;
    /* Remove fixed aspect ratio for rotated content */
    max-width: none;
    max-height: none;
    margin: 0;
  }
  
  .legend-content {
    padding: 10px;
  }
  
  .location-item {
    padding: 12px;
    margin-bottom: 15px;
  }
}
</style>

<script src="/static/js/beyogluMapOverlay.js"></script> 