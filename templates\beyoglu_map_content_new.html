<!-- Beyoglu Map Content -->
<div class="beyoglu-offline-container">
  <div class="beyoglu-map-title">Historic Locations in Beyoğlu</div>
  <div id="beyoglu-map"></div>
  <div class="beyoglu-map-info">
    <p>This map shows key historic locations in Beyoğlu where criminal activities took place. Each marker represents a different person or location. Click on markers to see details.</p>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Wait for Firebase to be initialized
  function initBeyogluMap() {
    if (!window.db) {
      console.log("Waiting for Firebase to initialize...");
      setTimeout(initBeyogluMap, 100);
      return;
    }

    console.log("Initializing Beyoğlu map with Firebase data...");

    // Initialize the map
    const beyogluMap = L.map('beyoglu-map').setView([41.0305, 28.9765], 14);

    // Add a tile layer with a sepia filter for an older look
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors',
      className: 'beyoglu-map-tiles' // Apply sepia filter via CSS
    }).addTo(beyogluMap);

    // Individual colors for each person/location
    const locationColors = {
      "point_giuseppe_civicoff": "#e74c3c", // Red
      "point_giuseppe_lopetz": "#3498db", // Blue
      "point_roberto_diamanti": "#2ecc71", // Green
      "point_petris_kalyoncu": "#e67e22", // Orange
      "point_gaetano_prado": "#9b59b6", // Purple
      "point_tekke_home": "#8B4513", // Brown
      "point_tepebasi_shop": "#1abc9c", // Turquoise
      "point_romano_factory": "#f39c12", // Yellow
      "point_charalanpas": "#34495e", // Dark Blue
      "point_bulbul_cafe": "#16a085", // Sea Green
      "point_cafe_tekke": "#d35400", // Pumpkin
      "point_liquor_factory": "#8e44ad", // Wisteria
      "point_english_casino": "#c0392b", // Pomegranate
      "point_theatre_cafe": "#2980b9" // Belize Hole
    };

    // Create custom icon for each location
    function createCustomIcon(locationId, data) {
      const color = locationColors[locationId] || "#999999";
      return L.divIcon({
        className: 'custom-marker',
        html: `<div style="background-color: ${color}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
        iconSize: [16, 16],
        iconAnchor: [8, 8],
        popupAnchor: [0, -8]
      });
    }

    // Fetch data from Firebase beyoglu collection
    window.db.collection('beyoglu').get().then((querySnapshot) => {
      console.log(`Found ${querySnapshot.size} locations in beyoglu collection`);

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const locationId = doc.id;

        console.log(`Processing location: ${locationId}`, data);

        // Check if location has coordinates
        if (data.coordinates && data.coordinates.lat && data.coordinates.lng) {
          const marker = L.marker(
            [data.coordinates.lat, data.coordinates.lng],
            { icon: createCustomIcon(locationId, data) }
          ).addTo(beyogluMap);

          // Create popup content
          const popupContent = `
            <div class="beyoglu-popup">
              <h3>${data.name || 'Unknown Location'}</h3>
              <p>${data.description || 'Historic location in Beyoğlu'}</p>
              ${data.network ? `<p><strong>Network:</strong> ${data.network}</p>` : ''}
              ${data.person ? `<p><strong>Person:</strong> ${data.person}</p>` : ''}
              ${data.type ? `<p><strong>Type:</strong> ${data.type}</p>` : ''}
            </div>
          `;

          marker.bindPopup(popupContent);
        } else {
          console.warn(`Location ${locationId} missing coordinates:`, data);
        }
      });
    }).catch((error) => {
      console.error("Error fetching beyoglu data:", error);

      // Fallback to static data if Firebase fails
      console.log("Using fallback static data...");
      loadStaticBeyogluData(beyogluMap, createCustomIcon);
    });
  }

  // Fallback function with static data
  function loadStaticBeyogluData(beyogluMap, createCustomIcon) {
    const historicLocations = {
      "point_giuseppe_civicoff": {
        "name": "Giuseppe Civicoff - Hotel de France",
        "description": "Historic location marker: Giuseppe Civicoff - Hotel de France",
        "coordinates": {
          "lat": 41.030589315603166,
          "lng": 28.97649776160499
        }
      },
      "point_giuseppe_lopetz": {
        "name": "Giuseppe Lopetz - Near Court of Galata",
        "description": "Historic location marker: Giuseppe Lopetz - Near Court of Galata",
        "coordinates": {
          "lat": 41.0242913344432,
          "lng": 28.968838573507902
        }
      },
      "point_roberto_diamanti": {
        "name": "Roberto Diamanti - Yeniçarşı Shop",
        "description": "Historic location marker: Roberto Diamanti - Yeniçarşı Shop",
        "coordinates": {
          "lat": 41.030303519766704,
          "lng": 28.9780009733551
        }
      }
    };

    // Add static markers to the map
    for (const id in historicLocations) {
      const location = historicLocations[id];
      if (location.coordinates?.lat && location.coordinates?.lng) {
        const marker = L.marker(
          [location.coordinates.lat, location.coordinates.lng],
          { icon: createCustomIcon(id, location) }
        ).addTo(beyogluMap);

        marker.bindPopup(`
          <div class="beyoglu-popup">
            <h3>${location.name}</h3>
            <p>${location.description}</p>
          </div>
        `);
      }
    }
  }

  // Start initialization
  initBeyogluMap();
});
</script>
