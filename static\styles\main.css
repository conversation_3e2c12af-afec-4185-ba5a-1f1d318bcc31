/* Color variables are now imported from colors.css */

body {
    margin: 0;
    padding: 0;
    font-family: 'Montserrat', sans-serif;
    overflow-x: hidden;
    background-color: var(--neutral-light);
    color: var(--neutral-dark);
}

/* Graphcommons container styling */
.graphcommons-container {
    width: 100%;
    height: 600px;
    margin: 20px 0;
    border: 2px solid var(--accent);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    background-color: var(--neutral-light);
    position: relative;
}

.graphcommons-container #network {
    width: 100%;
    height: 100%;
    background-color: var(--neutral-light);
}

.graphcommons-container #info-box {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--accent);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    max-width: 300px;
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    line-height: 1.4;
    backdrop-filter: blur(5px);
}

.graphcommons-container #info-box strong {
    color: var(--primary);
    font-weight: 600;
    display: block;
    margin-bottom: 8px;
    font-family: 'Playfair Display', serif;
}

/* Responsive design for graphcommons */
@media (max-width: 768px) {
    .graphcommons-container {
        height: 400px;
        margin: 15px 0;
    }
    
    .graphcommons-container #info-box {
        max-width: 250px;
        font-size: 13px;
        padding: 12px;
    }
}

@media (max-width: 480px) {
    .graphcommons-container {
        height: 350px;
        margin: 10px 0;
    }
    
    .graphcommons-container #info-box {
        max-width: 200px;
        font-size: 12px;
        padding: 10px;
        top: 5px;
        right: 5px;
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    color: var(--primary);
}

#time-slider-container {
    height: 100px;
    padding: 20px;
    background-color: var(--neutral-light);
    border-top: 1px solid var(--accent);
    display: flex;
    flex-direction: column;
    box-shadow: 0 -2px 10px rgba(75, 62, 42, 0.15);
    z-index: 2;
    position: relative;
}
#time-slider {
    margin: 15px 30px;
    height: 20px;
}
#current-year {
    text-align: center;
    font-size: 28px;
    font-family: 'Playfair Display', serif;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--primary);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    letter-spacing: 1px;
}
#time-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    position: fixed; /* Fixed position to keep it in view */
    bottom: 30px; /* Position at bottom of viewport, moved higher */
    right: 30px; /* Position at right side */
    z-index: 1000; /* Very high z-index to ensure it's above everything */
    background-color: var(--neutral-light); /* Semi-transparent background */
    padding: 8px 12px;
    border-radius: 30px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.25);
    border: 1px solid var(--accent);
}
#play-pause {
    background-color: var(--primary);
    color: var(--neutral-light);
    border: none;
    border-radius: 50%;
    width: 50px; /* Larger by default */
    height: 50px;
    font-size: 20px;
    cursor: pointer;
    outline: none;
    margin: 0 10px; /* Add margin for spacing between buttons */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.3);
    z-index: 1001; /* Higher than the container */
    transition: all 0.3s ease;
}

#prev-year, #next-year {
    background-color: var(--secondary); /* Secondary color for navigation buttons */
    color: var(--neutral-light);
    border: none;
    border-radius: 50%;
    width: 40px; /* Slightly smaller than play button */
    height: 40px;
    font-size: 18px;
    cursor: pointer;
    outline: none;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    z-index: 1001;
    transition: all 0.3s ease;
}

#prev-year:hover, #next-year:hover {
    background-color: var(--data-highlight); /* Highlight color on hover */
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
}
#play-pause:hover {
    background-color: var(--alert-red);
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.4);
}
.noUi-connect {
    background: linear-gradient(to right, #4B6455, #A67B5B);
}
.noUi-handle {
    border-radius: 50%;
    background: var(--neutral-light);
    border: 2px solid var(--primary) !important;
    box-shadow: 0 0 8px rgba(0,0,0,0.25);
    cursor: pointer;
    width: 24px !important;
    height: 24px !important;
    right: -12px !important;
    top: -5px !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.noUi-handle:hover {
    transform: scale(1.1);
    box-shadow: 0 0 12px rgba(0,0,0,0.3);
}
.noUi-handle:before, .noUi-handle:after {
    display: none;
}
.noUi-marker-horizontal.noUi-marker {
    height: 8px;
    background-color: var(--accent);
}
.noUi-value-horizontal {
    margin-top: 10px;
    font-family: 'Playfair Display', serif;
    color: var(--primary);
    font-weight: 500;
}
.marker-popup {
    max-width: 320px;
    font-family: 'Inter', sans-serif;
    width: 100%;
}

/* Override Leaflet's default popup styles for better responsiveness */
.leaflet-popup-content {
    margin: 10px;
    width: auto !important;
    max-width: 100%;
}

.leaflet-popup {
    max-width: 90vw;
}
.marker-details {
    padding: 12px;
    line-height: 1.5;
}
.marker-title {
    font-family: 'Playfair Display', serif;
    font-weight: bold;
    margin-bottom: 8px;
    color: var(--primary);
    font-size: 16px;
    border-bottom: 1px solid var(--accent);
    padding-bottom: 5px;
}
.marker-description {
    margin-bottom: 8px;
    color: var(--neutral-dark);
}
.marker-link {
    color: var(--data-highlight);
    cursor: pointer;
    text-decoration: underline;
    font-weight: 500;
    transition: color 0.2s ease;
}
.marker-link:hover {
    color: var(--primary);
}
.legend {
    position: absolute;
    bottom: 110px;
    right: 20px;
    background: var(--neutral-light);
    padding: 12px 15px;
    border-radius: 8px;
    border: 1px solid var(--accent);
    z-index: 500;
    max-width: 220px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 15px rgba(0,0,0,0.15);
    font-family: 'Inter', sans-serif;
}
.legend::before {
    content: 'Event Types';
    display: block;
    font-family: 'Playfair Display', serif;
    font-weight: bold;
    color: var(--primary);
    margin-bottom: 8px;
    border-bottom: 1px solid var(--accent);
    padding-bottom: 5px;
    font-size: 14px;
}
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: var(--neutral-dark);
}
.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.5);
}
.reset-button, .map-button {
    position: absolute;
    z-index: 500;
    background: var(--neutral-light);
    border: 1px solid var(--accent);
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    font-family: 'Inter', sans-serif;
    color: var(--primary);
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}
.reset-button:hover, .map-button:hover {
    background: var(--accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}


/* Unified marker styles */
.marker-icon {
    background-color: transparent;
    border: none;
}

.marker-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.marker-label {
    background-color: var(--neutral-light);
    border: 1px solid var(--accent);
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 10px;
    font-weight: 500;
    font-family: 'Inter', sans-serif;
    white-space: normal;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    color: var(--primary);
    margin-top: 3px;
    max-width: 100px;
    text-align: center;
    line-height: 1.2;
    word-wrap: break-word;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Location symbol marker (using Font Awesome icons) */
.marker-location-symbol {
    transition: all 0.3s ease;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    animation: pulse 2.5s infinite;
}

.marker-location-symbol i {
    font-size: inherit;
    color: inherit;
}

.marker-location-symbol:hover {
    transform: scale(1.5);
    text-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

/* Animation for all markers */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.9; }
    100% { transform: scale(1); opacity: 1; }
}

.ottoman-label div {
    background-color: rgba(75, 62, 42, 0.85);
    color: var(--neutral-light);
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Playfair Display', serif;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
    pointer-events: none;
    border: 1px solid rgba(212, 196, 168, 0.5);
    letter-spacing: 0.5px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

/* Custom cluster icon styles */
.custom-cluster-icon {
    background-color: transparent;
    text-align: center;
}

.cluster-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--neutral-light);
    font-weight: bold;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #4B6455, var(--secondary));
    animation: pulse-cluster 2.5s infinite;
    border: 2px solid var(--neutral-light);
    cursor: pointer; /* Make it clear it's clickable */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Hover effect to indicate clickability */
.cluster-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.6);
}

/* Cluster type-specific colors */
.cluster-forgery {
    background: linear-gradient(135deg, var(--alert-red), var(--secondary));
}

.cluster-escape {
    background: linear-gradient(135deg, #4B6455, var(--primary));
}

.cluster-arrest {
    background: linear-gradient(135deg, var(--neutral-dark), var(--primary));
}

.cluster-mixed {
    background: linear-gradient(135deg, #6A5D4D, var(--secondary));
}

.cluster-small {
    font-size: 14px;
    width: 36px;
    height: 36px;
}

.cluster-medium {
    font-size: 17px;
    width: 44px;
    height: 44px;
}

.cluster-large {
    font-size: 19px;
    width: 52px;
    height: 52px;
}

@keyframes pulse-cluster {
    0% { transform: scale(1); opacity: 0.9; }
    50% { transform: scale(1.08); opacity: 1; }
    100% { transform: scale(1); opacity: 0.9; }
}

/* Spiderfied marker styles */
.leaflet-marker-icon.leaflet-marker-icon-spiderfied {
    transition: all 0.3s ease;
    transform: scale(1.3) !important;
    z-index: 1000 !important;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.7);
    border: 2px solid var(--neutral-light) !important;
}

/* Spiderfy leg styles */
.leaflet-marker-shadow.leaflet-marker-shadow-spiderfied {
    display: none;
}

.leaflet-marker-icon.marker-cluster.leaflet-zoom-animated.leaflet-interactive {
    transition: all 0.3s ease;
}

/* Improve spiderfy legs */
.leaflet-marker-icon.marker-cluster-spiderfied {
    background-color: transparent !important;
    box-shadow: none !important;
}

.leaflet-marker-icon.marker-cluster-spiderfied div {
    display: none !important;
}

/* Style the spiderfy legs */
.leaflet-marker-icon-spiderfied-line {
    stroke-width: 2.5px !important;
    opacity: 0.8 !important;
    stroke: var(--primary) !important;
}

/* Numbered marker styles */
.custom-numbered-marker {
    background: none;
    border: none;
}

.marker-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 16px;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
    border: 2px solid var(--neutral-light);
    animation: pulse 2.5s infinite;
}

/* Custom map styling */
.historical-map-tiles {
    filter: sepia(30%) saturate(70%) brightness(95%) contrast(90%);
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    /* Hero section adjustments for tablets */
    .hero-section {
        height: 50vh;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    /* Navigation adjustments for tablets */
    .site-nav ul {
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .site-nav li {
        margin-right: 15px;
        margin-bottom: 5px;
    }

    .site-nav a {
        font-size: 1rem;
    }

    #time-slider-container {
        height: 120px;
        padding: 10px;
    }

    #time-slider {
        margin: 10px 15px;
    }

    #current-year {
        font-size: 20px;
    }

    /* No need for special tablet styling as we're using fixed positioning */

    .legend {
        bottom: 130px;
        right: 5px;
        padding: 5px;
        max-width: 150px;
        font-size: 12px;
    }

    .legend-item {
        margin-bottom: 3px;
    }

    .reset-button, .map-button {
        padding: 4px 8px;
        font-size: 12px;
    }

    .marker-label {
        font-size: 8px;
        padding: 2px 4px;
        max-width: 80px;
    }

    .ottoman-label div {
        font-size: 12px;
        padding: 3px 6px;
        width: 100%;
        height: 100%;
    }

    /* Responsive section headings for tablets */
    .section h2 {
        font-size: 2rem;
        padding-bottom: 12px;
    }

    .section h2::after {
        width: 70px;
    }

    .section h3 {
        font-size: 1.5rem;
    }

    .section-container {
        padding: 0 30px;
    }
}

@media screen and (max-width: 480px) {
    /* Navigation adjustments for mobile */
    .site-nav ul {
        padding: 0 0 0 5px;
    }

    .site-nav li {
        margin-right: 10px;
        margin-bottom: 8px;
    }

    .site-nav a {
        font-size: 0.9rem;
        padding: 3px 0;
    }

    .header-container {
        padding: 10px 15px;
    }

    #time-slider-container {
        height: 140px;
    }

    .noUi-value-horizontal {
        display: none;
    }

    #time-controls {
        padding: 5px;
        bottom: 15px;
        right: 15px;
    }

    #play-pause {
        width: 40px; /* Maintain size for small screens */
        height: 40px;
        font-size: 18px;
        margin: 0 5px;
    }

    #prev-year, #next-year {
        width: 32px; /* Slightly smaller on mobile */
        height: 32px;
        font-size: 14px;
    }

    .legend {
        bottom: 150px;
        max-width: 120px;
    }

    #toggle-borders {
        right: 80px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .marker-label {
        font-size: 7px;
        padding: 2px 3px;
        max-width: 60px;
        line-height: 1.1;
    }

    .ottoman-label div {
        font-size: 10px;
        padding: 2px 4px;
    }

    /* Improve popup readability on small screens */
    .marker-popup {
        max-width: 250px;
    }

    .marker-title {
        font-size: 14px;
    }

    .marker-description {
        font-size: 12px;
    }

    /* Responsive section headings */
    .section h2 {
        font-size: 1.8rem;
        padding-bottom: 12px;
    }

    .section h2::after {
        width: 60px;
        height: 2px;
    }

    .section h3 {
        font-size: 1.3rem;
        padding-left: 0;
        margin-bottom: 15px;
    }

    .section h3::after {
        width: 50px;
        height: 1px;
    }

    h4 {
        font-size: 1.1rem;
    }
}

/* Landing Page Styles */
/* These styles are for the landing page and are kept from the old CSS */

/* General Styles for Landing Page */
.landing-page {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

/* Header Styles */
.site-header {
    background-color: var(--primary); /* Dark navy blue */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.header-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 15px 20px; /* Increased vertical padding from 10px to 15px */
    display: flex;
    justify-content: flex-start; /* Align to the left */
    align-items: center;
}

.site-title {
    color: #fff;
    margin: 0;
    font-size: 1.5rem;
}

.site-nav ul {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0 0 0 10px; /* Added left padding */
}

.site-nav li {
    margin-right: 25px; /* Changed from margin-left to margin-right */
}

.site-nav li:last-child {
    margin-right: 0; /* Remove margin from last item */
}

.site-nav a {
    color: #fff;
    text-decoration: none;
    font-size: 1.1rem; /* Increased from 0.9rem to 1.1rem */
    padding: 5px 0;
    transition: color 0.3s;
    font-weight: 500; /* Added medium font weight for better visibility */
}

.site-nav a:hover {
    color: var(--accent);
}

.site-nav a.active {
    color: var(--accent);
    border-bottom: 2px solid var(--accent);
}

/* Hero Section */
.hero-section {
    height: 60vh; /* Reduced from 100vh to 60vh */
    background-image: linear-gradient(rgba(26, 42, 64, 0.9), rgba(26, 42, 64, 0.7)), url('/static/images/banner.jpeg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    padding: 0 20px;
}

.hero-content {
    width: 100%;
    max-width: 800px; /* Limit width for better readability */
    padding: 0 40px;
    margin: 0 auto; /* Center the content */
}

.hero-content h1 {
    font-size: 2.2rem;
    margin-bottom: 15px;
    color: var(--white);
}

.subtitle {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

/* Section Styles */
.section {
    padding: 80px 0; /* Remove horizontal padding */
    scroll-margin-top: 80px; /* Increased from 70px to 80px to account for taller header */
}

/* All sections have white background */
.section {
    background-color: var(--neutral-light); /* White */
}

.section-container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px; /* Increased padding to prevent content from going beyond screen edges */
    box-sizing: border-box;
}

.section-title-wrapper {
    text-align: center;
    width: 100%;
    margin-bottom: 10px;
}

.section-subtitle-wrapper {
    text-align: left;
    width: 100%;
    margin-bottom: 10px;
    padding-left: 0;
}

.section h2 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.9rem;
    color: var(--primary);
    position: relative;
    padding-bottom: 12px;
    font-family: 'Playfair Display', serif;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-left: auto;
    margin-right: auto;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, var(--accent), var(--primary));
    border-radius: 2px;
}

.section h3 {
    font-size: 1.6rem;
    margin-top: 40px;
    margin-bottom: 25px;
    color: var(--primary);
    position: relative;
    padding: 0;
    font-family: 'Playfair Display', serif;
    text-align: left;
    display: inline-block;
}

.section h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 80px;
    height: 2px;
    background-color: var(--accent);
    border-radius: 2px;
}

.section p {
    max-width: 1200px; /* Increased paragraph width for better content display */
    margin-left: auto;
    margin-right: auto;
    padding: 15px 20px;
    background-color: var(--mint-beige);
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    line-height: 1.6;
    font-family: 'Montserrat', sans-serif;
}

/* Map Container */
.map-container {
    width: 100%;
    height: 500px;
    background-color: var(--mint-beige);
    border-radius: 5px;
    margin-top: 20px;
    margin-left: 0;
    margin-right: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden; /* Ensure map content doesn't overflow */
}

/* Gallery Container */
.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px auto 0;
    max-width: 1200px; /* Limit width but still allow multiple columns */
}

/* Criminals Table Styles */
.criminals-table-container {
    width: 100%;
    max-width: 1200px; /* Limit width for better readability */
    overflow-x: auto;
    margin: 30px auto; /* Center the table */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    max-height: 600px; /* Increased height to show more rows */
    overflow-y: auto; /* Enable vertical scrolling */
}

.criminals-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
    font-size: 0.95rem;
}

.criminals-table th,
.criminals-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.criminals-table th {
    background-color: var(--primary);
    color: #fff;
    font-weight: 600;
    position: sticky;
    top: 0; /* Keep header visible when scrolling */
    z-index: 10; /* Ensure header stays above content when scrolling */
}

.criminals-table tr:nth-child(even) {
    background-color: var(--mint-beige);
}

.criminals-table tr:hover {
    background-color: rgba(232, 240, 230, 0.7); /* Lighter mint beige */
}

/* Criminal Portrait Card */
.criminal-portrait-card {
    display: flex;
    flex-direction: column;
    background-color: var(--mint-beige);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.criminal-portrait-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.portrait-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.portrait-info {
    padding: 15px;
}

.portrait-name {
    margin: 0 0 8px 0;
    color: var(--primary);
    font-size: 1.1rem;
    font-family: 'Playfair Display', serif;
    letter-spacing: 0.3px;
}

.portrait-description {
    margin: 0;
    color: #555;
    font-size: 0.9rem;
}

/* Network Container */
.network-container {
    width: 100%;
    height: 500px;
    background-color: #eee;
    border-radius: 5px;
    margin-top: 20px;
    margin-left: 0;
    margin-right: 0;
}

/* Team Container */
.team-container {
    margin: 30px auto 0;
    max-width: 1200px; /* Limit width */
}

/* Team Row */
.team-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 30px;
}

@media (max-width: 768px) {
    .team-row {
        flex-direction: column;
    }
}

/* Supervisor Container */
.supervisor-container {
    display: flex;
    justify-content: center;
    margin: 30px auto 0;
    max-width: 1200px; /* Limit width */
}

/* Team Member Card */
.team-member-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--mint-beige);
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex: 1;
    width: 100%;
}

.team-member-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.member-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 15px;
    border: 3px solid var(--primary);
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-info {
    text-align: center;
    width: 100%;
}

.member-name {
    margin: 0 0 10px 0;
    color: var(--primary);
    font-size: 1.2rem;
    font-family: 'Playfair Display', serif;
}

.member-title {
    margin: 0 0 5px 0;
    color: #555;
    font-size: 0.9rem;
}

.member-program {
    margin: 0 0 5px 0;
    color: #555;
    font-size: 0.9rem;
}

.member-institution {
    margin: 0;
    color: #777;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Image Container Styles */
.image-container {
    max-width: 800px; /* Limit image width */
    margin: 20px auto; /* Center the image container */
    overflow: hidden;
}

.image-container img {
    max-width: 100%;
    height: auto;
    display: block;
    border-radius: 5px;
}

.image-caption {
    font-size: 0.9rem;
    color: #666;
    margin-top: 8px;
    text-align: center;
    font-style: normal; /* Normal text style by default */
}

/* Italicize only book/work titles within captions */
.image-caption em {
    font-style: italic; /* Italicize text within <em> tags */
}

/* Side by side images */
.image-row {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    flex-wrap: wrap;
}

.image-container.side-by-side {
    width: 48%; /* Slightly less than 50% to account for spacing */
    margin: 0;
}

/* Responsive adjustment for small screens */
@media (max-width: 768px) {
    .image-container.side-by-side {
        width: 100%;
        margin: 10px 0;
    }
}

/* Timeline Styles */
.timeline-container {
    position: relative;
    max-width: 900px;
    margin: 20px auto 40px;
    padding: 20px 20px 20px 40px;
    background-color: var(--mint-beige);
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary);
    opacity: 0.7;
    border-radius: 1px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 25px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -9px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent), var(--primary));
    border: 2px solid #fff;
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.timeline-item:hover::before {
    transform: scale(1.2);
}

.timeline-date {
    font-weight: bold;
    color: var(--primary);
    margin-bottom: 5px;
    font-family: 'Playfair Display', serif;
}

.timeline-content {
    color: var(--neutral-dark);
    line-height: 1.6;
    font-family: 'Montserrat', sans-serif;
}

h4 {
    font-size: 1.3rem;
    margin-top: 30px;
    margin-bottom: 15px;
    color: var(--primary);
    font-family: 'Playfair Display', serif;
    position: relative;
    display: inline-block;
}

h4::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent);
    opacity: 0.5;
}

/* Modal styles from old CSS */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
}

.modal-content {
    background-color: #f8f8f8;
    margin: 10% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}