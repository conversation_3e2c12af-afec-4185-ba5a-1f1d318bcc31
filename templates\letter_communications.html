
<link rel="stylesheet" href="/static/styles/letter_communications.css">
<div class="section-container">
    <!-- Diplomat selector section -->
    <div class="section-header">
        <div class="section-subtitle-wrapper">
        </div>
        <div id="diplomat-selector-container">
            <select id="diplomat-selector">
                <option value="">-- Select a diplomat --</option>
            </select>
        </div>
    </div>

    <!-- Diplomat description box -->
    <div id="diplomat-description">
        <p class="initial-message">Select a diplomat to view their communications</p>
    </div>

    <!-- Communications map container -->
    <div class="map-container-communications" >
        <div id="communications-map" ></div>
        
        <!-- Communications info panel -->
        <div id="communications-info" class="info-panel">
            <h3>Communications</h3>
            <div id="communications-events">
                <!-- Communication events will be populated dynamically -->
            </div>
        </div>
    </div>
