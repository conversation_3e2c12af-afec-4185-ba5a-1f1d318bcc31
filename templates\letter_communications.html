
<link rel="stylesheet" href="/static/styles/letter_communications.css">
<div class="section-container">
    <!-- Diplomat selector section -->
    <div class="section-header">
        <div class="section-subtitle-wrapper">
        </div>
        <div id="diplomat-selector-container">
            <select id="diplomat-selector">
                <option value="">-- Select a diplomat --</option>
            </select>
        </div>
    </div>

    <!-- Diplomat description box -->
    <div id="diplomat-description">
        <p class="initial-message">Select a diplomat to view their communications</p>
    </div>

    <!-- Communications map container -->
    <div class="map-container-communications" >
        <div id="communications-map" ></div>
        
        <!-- Communications info panel -->
        <div id="communications-info" class="info-panel">
            <h3>Communications</h3>
            <div id="communications-events">
                <!-- Communication events will be populated dynamically -->
            </div>
        </div>
    </div>

    <!-- Diplomat Gallery Slider -->
    <div class="diplomat-gallery-section">
        <div class="section-subtitle-wrapper">
            <h3>Diplomat Portraits</h3>
        </div>
        <div class="diplomat-gallery-container">
            <button class="gallery-nav prev" id="gallery-prev">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="gallery-nav next" id="gallery-next">
                <i class="fas fa-chevron-right"></i>
            </button>
            
            <div class="diplomat-gallery" id="diplomat-gallery">
                <!-- Diplomat slides will be populated via JavaScript -->
            </div>
            
            <div class="gallery-dots" id="gallery-dots">
                <!-- Dots will be populated via JavaScript -->
            </div>
        </div>
    </div>
</div>
