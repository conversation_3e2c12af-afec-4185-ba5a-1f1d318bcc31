<!-- Criminal journeys section with title and selector -->
    <div class="section-container">
        <div class="section-header">
            <div class="section-subtitle-wrapper">
                
            </div>
            <select id="criminal-selector" >
                <option value="">-- Select a criminal --</option>
                <!-- Options will be populated dynamically -->
            </select>
            
        </div>

        <!-- Criminal description text box below the selector -->
        <div id="criminal-description" class="description-box">
            <!-- Criminal description will be populated dynamically -->
            <p class="initial-message">Select a criminal to view details</p>
        </div>
    </div>

    <!-- Map container below the selector and description -->
    <div id="map-container">
        <div id="criminals-map"></div>

        <!-- Timeline info panel for criminal journeys -->
        <div id="timeline-info" class="info-panel">
            <h3>Criminal Journey</h3>
            <div id="timeline-events">
                <!-- Timeline events will be populated dynamically -->
            </div>
        </div>
    </div>

    <!-- Criminal info panel is now replaced by the criminal-description above the map -->

    <!-- Firebase configuration data -->
    <script id="firebase-config" type="application/json">
        {{ firebase_config | tojson }}
    </script>

    <!-- External libraries -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.10/firebase-firestore-compat.js"></script>
    <script src="https://unpkg.com/leaflet-polylinedecorator/dist/leaflet.polylineDecorator.js"></script>
    <script>
        // Fallback if the plugin fails to load
        if (!L.polylineDecorator) {
            L.polylineDecorator = function(polyline) {
                console.warn('Using polylineDecorator fallback');
                return {
                    addTo: function() { return this; }
                };
            };
        }
    </script>
