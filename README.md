# ottoman_crimenet_map
PURE '2025 at Sabanci University
Modularized the Ottoman Crime Network Map application by separating concerns into different files:

CSS Styles:
Moved all styles to static/styles/main.css
JavaScript Modules:
map.js - Core map functionality
timeSlider.js - Time slider functionality
markers.js - Marker creation and handling
criminals.js - Criminal data display
borders.js - Ottoman borders functionality
main.js - Application initialization
HTML Structure:
Updated index.html to use the modular structure
Added proper script and stylesheet references