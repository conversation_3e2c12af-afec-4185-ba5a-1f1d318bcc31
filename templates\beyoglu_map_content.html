<!-- Beyoglu Map Content -->
<div class="beyoglu-offline-container">
  <div class="beyoglu-map-title">Historic Locations in Beyoğlu</div>
  <div id="beyoglu-map"></div>
  <div class="beyoglu-map-info">
    <p>This map shows key historic locations in Beyoğlu where criminal activities took place. Each marker represents a different person or location. Click on markers to see details.</p>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize the map
    const beyogluMap = L.map('beyoglu-map').setView([41.0305, 28.9765], 14);

    // Add a tile layer with a sepia filter for an older look
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors',
      className: 'beyoglu-map-tiles' // Apply sepia filter via CSS
    }).addTo(beyogluMap);

    // Historic location data
    const historicLocations = {
      "point_giuseppe_civicoff": {
        "name": "Giuseppe Civicoff - Hotel de France",
        "description": "Historic location marker: Giuseppe Civicoff - Hotel de France",
        "coordinates": {
          "lat": 41.030589315603166,
          "lng": 28.97649776160499
        }
      },
      "point_giuseppe_lopetz": {
        "name": "Giuseppe Lopetz - Near Court of Galata",
        "description": "Historic location marker: Giuseppe Lopetz - Near Court of Galata",
        "coordinates": {
          "lat": 41.0242913344432,
          "lng": 28.968838573507902
        }
      },
      "point_roberto_diamanti": {
        "name": "Roberto Diamanti - Yeniçarşı Shop",
        "description": "Historic location marker: Roberto Diamanti - Yeniçarşı Shop",
        "coordinates": {
          "lat": 41.030303519766704,
          "lng": 28.9780009733551
        }
      },
      "point_petris_kalyoncu": {
        "name": "Margarita & Francisco Petris - Kalyoncu Kulluk",
        "description": "Historic location marker: Margarita & Francisco Petris - Kalyoncu Kulluk",
        "coordinates": {
          "lat": 41.03290403312925,
          "lng": 28.97697673424401
        }
      },
      "point_gaetano_prado": {
        "name": "Gaetano Manzo - Café Prado",
        "description": "Historic location marker: Gaetano Manzo - Café Prado",
        "coordinates": {
          "lat": 41.03569381588824,
          "lng": 28.98558193126751
        },
        "network": "network_3"
      },
      "point_tekke_home": {
        "name": "Cesare Venzi & Draganikos - near Tekke",
        "description": "Historic location marker: Cesare Venzi & Draganikos - near Tekke",
        "coordinates": {
          "lat": 41.026273816355044,
          "lng": 28.974206021580866
        },
        "network": "network_3"
      },
      "point_tepebasi_shop": {
        "name": "Cesare Venzi & Draganikos - Tepebaşı shop",
        "description": "Historic location marker: Cesare Venzi & Draganikos - Tepebaşı shop",
        "coordinates": {
          "lat": 41.0343586031685,
          "lng": 28.9772272779388
        },
        "network": "network_4"
      },
      "point_romano_factory": {
        "name": "Giovanni Romano - Button Maker Factory",
        "description": "Historic location marker: Giovanni Romano - Button Maker Factory",
        "coordinates": {
          "lat": 41.037324200575725,
          "lng": 28.979366317555964
        },
        "network": "network_4"
      },
      "point_charalanpas": {
        "name": "Café Charalanpas - Manzo & Cole",
        "description": "Historic location marker: Café Charalanpas - Manzo & Cole",
        "coordinates": {
          "lat": 41.033066419093934,
          "lng": 28.977533906956232
        },
        "network": "network_5"
      },
      "point_bulbul_cafe": {
        "name": "Bülbül Café - Boschi & Manzo & Facchini",
        "description": "Historic location marker: Bülbül Café - Boschi & Manzo & Facchini",
        "coordinates": {
          "lat": 41.026846802379815,
          "lng": 28.97925182642846
        },
        "network": "network_5"
      },
      "point_cafe_tekke": {
        "name": "Café Tekke - Boschi, Manzo, Venzi, Draganikos",
        "description": "Historic location marker: Café Tekke - Boschi, Manzo, Venzi, Draganikos",
        "coordinates": {
          "lat": 41.0267144121676,
          "lng": 28.974235971836414
        },
        "network": "network_6"
      },
      "point_liquor_factory": {
        "name": "Liquor Factory - Taksim Fountain",
        "description": "Historic location marker: Liquor Factory - Taksim Fountain",
        "coordinates": {
          "lat": 41.034068294308014,
          "lng": 28.984525163211813
        },
        "network": "network_6"
      },
      "point_english_casino": {
        "name": "English Casino - Petris, Manzo, Cole",
        "description": "Historic location marker: English Casino - Petris, Manzo, Cole",
        "coordinates": {
          "lat": 41.02628024353369,
          "lng": 28.978441674784428
        },
        "network": "network_1"
      },
      "point_theatre_cafe": {
        "name": "Theatre Café - Boschi & Draganikos",
        "description": "Historic location marker: Theatre Café - Boschi & Draganikos",
        "coordinates": {
          "lat": 41.03201567929225,
          "lng": 28.979142110011946
        },
        "network": "network_2"
      }
    };

    // Individual colors for each person
    const personColors = {
      "point_giuseppe_civicoff": "#e74c3c", // Red
      "point_giuseppe_lopetz": "#3498db", // Blue
      "point_roberto_diamanti": "#2ecc71", // Green
      "point_petris_kalyoncu": "#e67e22", // Orange
      "point_gaetano_prado": "#9b59b6", // Purple
      "point_tekke_home": "#8B4513", // Brown
      "point_tepebasi_shop": "#1abc9c", // Turquoise
      "point_romano_factory": "#f39c12", // Yellow
      "point_charalanpas": "#34495e", // Dark Blue
      "point_bulbul_cafe": "#16a085", // Sea Green
      "point_cafe_tekke": "#d35400", // Pumpkin
      "point_liquor_factory": "#8e44ad", // Wisteria
      "point_english_casino": "#c0392b", // Pomegranate
      "point_theatre_cafe": "#2980b9" // Belize Hole
    };

    // Create custom icon for each person
    function createCustomIcon(personId) {
      return L.divIcon({
        className: 'custom-marker',
        html: `<div style="background-color: ${personColors[personId] || "#999999"}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
        iconSize: [16, 16],
        iconAnchor: [8, 8],
        popupAnchor: [0, -8]
      });
    }

    // Add markers to the map
    for (const id in historicLocations) {
      const location = historicLocations[id];
      if (location.coordinates?.lat && location.coordinates?.lng) {
        const marker = L.marker(
          [location.coordinates.lat, location.coordinates.lng],
          { icon: createCustomIcon(id) }
        ).addTo(beyogluMap);

        marker.bindPopup(`
          <div class="beyoglu-popup">
            <h3>${location.name}</h3>
            <p>${location.description}</p>
          </div>
        `);
      }
    }

    // No legend needed as each person has a unique color
  });
</script>
