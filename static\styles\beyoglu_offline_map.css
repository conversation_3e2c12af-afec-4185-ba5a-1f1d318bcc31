/* Beyoglu Offline Map Styles */

.beyoglu-offline-container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto 30px;
  padding: 15px;
  background-color: #f9f6f0;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.beyoglu-map-title {
  font-family: 'Playfair Display', serif;
  color: #8B4513;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

#beyoglu-map {
  height: 500px;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.beyoglu-map-info {
  margin-top: 15px;
  padding: 10px;
  background-color: rgba(139, 69, 19, 0.05);
  border-radius: 6px;
  font-style: italic;
  color: #666;
  text-align: center;
}

/* Apply sepia filter to map tiles for historic look */
.beyoglu-map-tiles {
  filter: sepia(20%) saturate(90%);
}

/* Custom popup styling */
.beyoglu-popup {
  padding: 5px;
}

.beyoglu-popup h3 {
  margin: 0 0 8px 0;
  color: #8B4513;
  font-size: 14px;
  font-weight: 600;
  font-family: 'Playfair Display', serif;
}

.beyoglu-popup p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

/* Legend styling */
.beyoglu-legend {
  background-color: white;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  line-height: 1.5;
  max-width: 180px;
}

.beyoglu-legend h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #8B4513;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .beyoglu-offline-container {
    padding: 10px;
  }
  
  #beyoglu-map {
    height: 400px;
  }
  
  .beyoglu-map-title {
    font-size: 1.1rem;
  }
}
