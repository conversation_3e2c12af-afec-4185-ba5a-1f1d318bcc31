<div style="display: flex; justify-content: center; width: 100%;">
  <div id="network" style="width: 90%; height: 900px; border: 1px solid #ccc;">
    <div id="loading-message" style="display: flex; justify-content: center; align-items: center; height: 100%; font-size: 18px; color: #666;">
      Loading network data...
    </div>
  </div>
</div>

<div id="info-box" style="display: none; position: fixed; background: white; border: 1px solid #ccc; padding: 10px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 9999; max-width: 300px; pointer-events: none;"></div>

<!-- vis-network JS CDN -->
<script src="https://unpkg.com/vis-network@9/dist/vis-network.min.js"></script>
<script>
// Global variables to store network data and instance
let network = null;
let nodes = [];
let edges = [];

// Function to initialize the network visualization
function initializeNetwork(data) {
  nodes = data.nodes.map(node => {
    const nodeType = data.nodeTypes.find(nt => nt.id === node.typeId);
    return {
      id: node.id,
      label: node.name || "UNKNOWN",
      title: node.name || "UNKNOWN",
      color: nodeType?.color || "#97C2FC",
      description: node.description || "No description available."
    };
  });

  edges = data.edges.map(edge => {
    const edgeType = data.edgeTypes.find(et => et.id === edge.typeId);
    return {
      from: edge.sourceId,
      to: edge.targetId,
      color: edgeType?.color || "#848484"
    };
  });

  const container = document.getElementById('network');
  const infoBox = document.getElementById('info-box');
  const networkData = { nodes, edges };
  const options = {
    nodes: {
      shape: 'dot',
      size: 12,
      font: {
        size: 14,
        color: "#333"
      }
    },
    edges: {
      color: { inherit: false },
      smooth: false
    },
    physics: {
      enabled: true,
      stabilization: {
        iterations: 250
      },
      barnesHut: {
        gravitationalConstant: -2000,
        centralGravity: 0.3,
        springLength: 95,
        springConstant: 0.04,
        damping: 0.09,
        avoidOverlap: 0.1
      }
    },
    interaction: {
      hover: true,
      tooltipDelay: 100,
      dragNodes: false,
      dragView: false,
      zoomView: false
    }
  };
  
  network = new vis.Network(container, networkData, options);

  // Click listener for popup
  network.on("click", function (params) {
    console.log("Click detected:", params);
    
    if (params.nodes.length > 0) {
      const nodeId = params.nodes[0];
      const node = nodes.find(n => n.id === nodeId);
      console.log("Node found:", node);
      
      if (node) {
        const infoBox = document.getElementById('info-box');
        const networkContainer = document.getElementById('network');
        console.log("Info box element:", infoBox);
        
        infoBox.innerHTML = `
          <strong>${node.label}</strong><br/>
          Place of Forgery: ${node.description || "No information available"}
        `;
        infoBox.style.display = "block";
        
        // Get the network container's position relative to viewport
        const containerRect = networkContainer.getBoundingClientRect();
        
        // Calculate position relative to the click point, accounting for scroll
        const x = params.pointer.DOM.x + 10;
        const y = params.pointer.DOM.y - 50;
        
        // Ensure the infobox stays within the viewport bounds
        const maxX = window.innerWidth - infoBox.offsetWidth - 10;
        const maxY = window.innerHeight - infoBox.offsetHeight - 10;
        
        infoBox.style.left = Math.min(Math.max(10, x), maxX) + "px";
        infoBox.style.top = Math.min(Math.max(10, y), maxY) + "px";
        
        console.log("Info box should be visible now");
      }
    } else {
      document.getElementById('info-box').style.display = "none"; 
    }
  });

  // Hide infobox when clicking outside of nodes
  network.on("blurNode", function (params) {
    document.getElementById('info-box').style.display = "none";
  });

  // Hide infobox when scrolling
  window.addEventListener('scroll', function() {
    document.getElementById('info-box').style.display = "none";
  });

  // Hide infobox when resizing window
  window.addEventListener('resize', function() {
    document.getElementById('info-box').style.display = "none";
  });

  // Hide infobox when clicking anywhere else on the page
  document.addEventListener('click', function(event) {
    const infoBox = document.getElementById('info-box');
    const networkContainer = document.getElementById('network');
    
    // Check if click is outside the network container
    if (!networkContainer.contains(event.target)) {
      infoBox.style.display = "none";
    }
  });
}

// Function to show error message
function showError(message) {
  const container = document.getElementById('network');
  container.innerHTML = `
    <div style="display: flex; justify-content: center; align-items: center; height: 100%; font-size: 18px; color: #d32f2f;">
      Error: ${message}
    </div>
  `;
}

// Load data asynchronously
async function loadNetworkData() {
  try {
    const response = await fetch('/static/images/graphcommons.json');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Hide loading message
    const loadingMessage = document.getElementById('loading-message');
    if (loadingMessage) {
      loadingMessage.style.display = 'none';
    }
    
    // Initialize the network with the loaded data
    initializeNetwork(data);
    
  } catch (error) {
    console.error('Error loading network data:', error);
    showError('Failed to load network data. Please try refreshing the page.');
  }
}

// Start loading the data when the script runs
loadNetworkData();
</script>