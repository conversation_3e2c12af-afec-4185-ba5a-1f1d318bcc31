
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Beyoğlu Map - Offline</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
  <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
  <style>
    body, html { margin: 0; padding: 0; }
    #map { height: 100vh; width: 100vw; }
  </style>
</head>
<body>
  <div id="map"></div>
  <script>
    const map = L.map('map').setView([41.0305, 28.9765], 14);
    <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    const data = {
  "point_giuseppe_civicoff": {
    "name": "<PERSON> - Hotel de France",
    "description": "Historic location marker: <PERSON> - Hotel de France",
    "coordinates": {
      "lat": 41.030589315603166,
      "lng": 28.97649776160499
    }
  },
  "point_giuseppe_lopetz": {
    "name": "Giuseppe Lopetz - Near Court of Galata",
    "description": "Historic location marker: Giuseppe Lopetz - Near Court of Galata",
    "coordinates": {
      "lat": 41.0242913344432,
      "lng": 28.968838573507902
    }
  },
  "point_roberto_diamanti": {
    "name": "Roberto Diamanti - Yeniçarşı Shop",
    "description": "Historic location marker: Roberto Diamanti - Yeniçarşı Shop",
    "coordinates": {
      "lat": 41.030303519766704,
      "lng": 28.9780009733551
    }
  },
  "point_petris_kalyoncu": {
    "name": "Margarita & Francisco Petris - Kalyoncu Kulluk",
    "description": "Historic location marker: Margarita & Francisco Petris - Kalyoncu Kulluk",
    "coordinates": {
      "lat": 41.03290403312925,
      "lng": 28.97697673424401
    }
  },
  "point_gaetano_prado": {
    "name": "Gaetano Manzo - Café Prado",
    "description": "Historic location marker: Gaetano Manzo - Café Prado",
    "coordinates": {
      "lat": 41.03569381588824,
      "lng": 28.98558193126751
    }
  },
  "point_tekke_home": {
    "name": "Cesare Venzi & Draganikos - near Tekke",
    "description": "Historic location marker: Cesare Venzi & Draganikos - near Tekke",
    "coordinates": {
      "lat": 41.026273816355044,
      "lng": 28.974206021580866
    }
  },
  "point_tepebasi_shop": {
    "name": "Cesare Venzi & Draganikos - Tepebaşı shop",
    "description": "Historic location marker: Cesare Venzi & Draganikos - Tepebaşı shop",
    "coordinates": {
      "lat": 41.0343586031685,
      "lng": 28.9772272779388
    }
  },
  "point_romano_factory": {
    "name": "Giovanni Romano - Button Maker Factory",
    "description": "Historic location marker: Giovanni Romano - Button Maker Factory",
    "coordinates": {
      "lat": 41.037324200575725,
      "lng": 28.979366317555964
    }
  },
  "point_charalanpas": {
    "name": "Café Charalanpas - Manzo & Cole",
    "description": "Historic location marker: Café Charalanpas - Manzo & Cole",
    "coordinates": {
      "lat": 41.033066419093934,
      "lng": 28.977533906956232
    }
  },
  "point_bulbul_cafe": {
    "name": "Bülbül Café - Boschi & Manzo & Facchini",
    "description": "Historic location marker: Bülbül Café - Boschi & Manzo & Facchini",
    "coordinates": {
      "lat": 41.026846802379815,
      "lng": 28.97925182642846
    }
  },
  "point_cafe_tekke": {
    "name": "Café Tekke - Boschi, Manzo, Venzi, Draganikos",
    "description": "Historic location marker: Café Tekke - Boschi, Manzo, Venzi, Draganikos",
    "coordinates": {
      "lat": 41.0267144121676,
      "lng": 28.974235971836414
    }
  },
  "point_liquor_factory": {
    "name": "Liquor Factory - Taksim Fountain",
    "description": "Historic location marker: Liquor Factory - Taksim Fountain",
    "coordinates": {
      "lat": 41.034068294308014,
      "lng": 28.984525163211813
    }
  },
  "point_english_casino": {
    "name": "English Casino - Petris, Manzo, Cole",
    "description": "Historic location marker: English Casino - Petris, Manzo, Cole",
    "coordinates": {
      "lat": 41.02628024353369,
      "lng": 28.978441674784428
    }
  },
  "point_theatre_cafe": {
    "name": "Theatre Café - Boschi & Draganikos",
    "description": "Historic location marker: Theatre Café - Boschi & Draganikos",
    "coordinates": {
      "lat": 41.03201567929225,
      "lng": 28.979142110011946
    }
  }
};

    for (const id in data) {
      const point = data[id];
      if (point.coordinates?.lat && point.coordinates?.lng) {
        const marker = L.marker([point.coordinates.lat, point.coordinates.lng]).addTo(map);
        marker.bindPopup(`<b>${point.name}</b><br>${point.description}`);
      }
    }
  </script>
</body>
</html>
